/**
 * Vue.js v3.4.38 - Deobfuscated Version
 * (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
 * @license MIT
 * 
 * This is a deobfuscated version of the original minified Vue.js file.
 * Variable names and function names have been restored to meaningful names.
 */

// Module loader utility function
var createModuleLoader = (moduleFactory, moduleExports) => () => (
    moduleExports || moduleFactory((moduleExports = { exports: {} }).exports, moduleExports),
    moduleExports.exports
);

// Vue.js module initialization
var vueModule = createModuleLoader((moduleExports, module) => {
    
    // Module preload polyfill
    (function initializeModulePreload() {
        const linkRelList = document.createElement("link").relList;
        if (linkRelList && linkRelList.supports && linkRelList.supports("modulepreload"))
            return;
        
        // Process existing modulepreload links
        for (const link of document.querySelectorAll('link[rel="modulepreload"]'))
            processModulePreloadLink(link);
        
        // Watch for new modulepreload links
        new MutationObserver(mutations => {
            for (const mutation of mutations)
                if (mutation.type === "childList")
                    for (const addedNode of mutation.addedNodes)
                        if (addedNode.tagName === "LINK" && addedNode.rel === "modulepreload") 
                            processModulePreloadLink(addedNode);
        }).observe(document, {
            childList: true,
            subtree: true
        });
        
        function createFetchOptions(link) {
            const options = {};
            if (link.integrity) options.integrity = link.integrity;
            if (link.referrerPolicy) options.referrerPolicy = link.referrerPolicy;
            
            if (link.crossOrigin === "use-credentials") {
                options.credentials = "include";
            } else if (link.crossOrigin === "anonymous") {
                options.credentials = "omit";
            } else {
                options.credentials = "same-origin";
            }
            return options;
        }
        
        function processModulePreloadLink(link) {
            if (link.ep) return; // Already processed
            link.ep = true;
            const fetchOptions = createFetchOptions(link);
            fetch(link.href, fetchOptions);
        }
    })();

    /**
     * @vue/shared v3.4.38 - Shared utilities
     */
    
    // Create a function that checks if a value is in a comma-separated list
    function makeMap(str, expectsLowerCase) {
        const map = new Set(str.split(","));
        return val => map.has(val);
    }
    
    // Common constants and utilities
    const EMPTY_OBJ = {};
    const EMPTY_ARR = [];
    const NOOP = () => {};
    const NO_OP = () => false;
    
    // Event handler detection
    const isOn = key => key.charCodeAt(0) === 111 && key.charCodeAt(1) === 110 && (key.charCodeAt(2) > 122 || key.charCodeAt(2) < 97);
    const isModelListener = key => key.startsWith("onUpdate:");
    
    // Object utilities
    const extend = Object.assign;
    const remove = (arr, el) => {
        const index = arr.indexOf(el);
        if (index > -1) arr.splice(index, 1);
    };
    
    const hasOwnProperty = Object.prototype.hasOwnProperty;
    const hasOwn = (val, key) => hasOwnProperty.call(val, key);
    
    // Type checking utilities
    const isArray = Array.isArray;
    const isMap = val => toTypeString(val) === "[object Map]";
    const isSet = val => toTypeString(val) === "[object Set]";
    const isFunction = val => typeof val === "function";
    const isString = val => typeof val === "string";
    const isSymbol = val => typeof val === "symbol";
    const isObject = val => val !== null && typeof val === "object";
    const isPromise = val => (isObject(val) || isFunction(val)) && isFunction(val.then) && isFunction(val.catch);
    
    const objectToString = Object.prototype.toString;
    const toTypeString = value => objectToString.call(value);
    const toRawType = value => toTypeString(value).slice(8, -1);
    const isPlainObject = val => toTypeString(val) === "[object Object]";
    const isIntegerKey = key => isString(key) && key !== "NaN" && key[0] !== "-" && "" + parseInt(key, 10) === key;
    
    // Reserved props for Vue internal use
    const isReservedProp = makeMap(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted");
    
    // Caching utility for string transformations
    const cacheStringFunction = fn => {
        const cache = Object.create(null);
        return str => cache[str] || (cache[str] = fn(str));
    };
    
    // String transformation utilities
    const camelizeRE = /-(\w)/g;
    const camelize = cacheStringFunction(str => str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : ""));
    const hyphenateRE = /\B([A-Z])/g;
    const hyphenate = cacheStringFunction(str => str.replace(hyphenateRE, "-$1").toLowerCase());
    const capitalize = cacheStringFunction(str => str.charAt(0).toUpperCase() + str.slice(1));
    const toHandlerKey = cacheStringFunction(str => str ? `on${capitalize(str)}` : "");
    
    // Comparison and utility functions
    const hasChanged = (value, oldValue) => !Object.is(value, oldValue);
    const invokeArrayFns = (fns, ...args) => {
        for (let i = 0; i < fns.length; i++)
            fns[i](...args);
    };
    
    const def = (obj, key, value, writable = false) => {
        Object.defineProperty(obj, key, {
            configurable: true,
            enumerable: false,
            writable: writable,
            value: value
        });
    };
    
    // Number parsing utilities
    const looseToNumber = val => {
        const n = parseFloat(val);
        return isNaN(n) ? val : n;
    };
    
    const toNumber = val => {
        const n = isString(val) ? Number(val) : NaN;
        return isNaN(n) ? val : n;
    };
    
    // Global object detection
    let globalObject;
    const getGlobalThis = () => globalObject || (globalObject = typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {});
    
    // Style normalization function
    function normalizeStyle(value) {
        if (isArray(value)) {
            const res = {};
            for (let i = 0; i < value.length; i++) {
                const item = value[i];
                const normalized = isString(item) ? parseStringStyle(item) : normalizeStyle(item);
                if (normalized) {
                    for (const key in normalized) {
                        res[key] = normalized[key];
                    }
                }
            }
            return res;
        } else if (isString(value) || isObject(value)) {
            return value;
        }
    }
    
    // CSS parsing regular expressions
    const listDelimiterRE = /;(?![^(]*\))/g;
    const propertyDelimiterRE = /:([^]+)/;
    const commentRE = /\/\*[^]*?\*\//g;
    
    function parseStringStyle(cssText) {
        const ret = {};
        cssText.replace(commentRE, "").split(listDelimiterRE).forEach(item => {
            if (item) {
                const tmp = item.split(propertyDelimiterRE);
                if (tmp.length > 1) {
                    ret[tmp[0].trim()] = tmp[1].trim();
                }
            }
        });
        return ret;
    }
    
    // Class name normalization
    function normalizeClass(value) {
        let res = "";
        if (isString(value)) {
            res = value;
        } else if (isArray(value)) {
            for (let i = 0; i < value.length; i++) {
                const normalized = normalizeClass(value[i]);
                if (normalized) {
                    res += normalized + " ";
                }
            }
        } else if (isObject(value)) {
            for (const name in value) {
                if (value[name]) {
                    res += name + " ";
                }
            }
        }
        return res.trim();
    }
    
    // Boolean attributes
    const specialBooleanAttrs = "itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly";
    const isSpecialBooleanAttr = makeMap(specialBooleanAttrs);
    
    function includeBooleanAttr(value) {
        return !!value || value === "";
    }
    
    // Ref utilities
    const isRef = val => !!(val && val.__v_isRef === true);
    
    // String conversion with ref handling
    const toDisplayString = val => isString(val) ? val : val == null ? "" : isArray(val) || (isObject(val) && (val.toString === objectToString || !isFunction(val.toString))) ? isRef(val) ? toDisplayString(val.value) : JSON.stringify(val, replacerForDisplay, 2) : String(val);
    
    const replacerForDisplay = (key, val) => isRef(val) ? replacerForDisplay(key, val.value) : isMap(val) ? {
        [`Map(${val.size})`]: [...val.entries()].reduce((entries, [key, val], i) => {
            entries[stringifySymbol(key, i) + " =>"] = val;
            return entries;
        }, {})
    } : isSet(val) ? {
        [`Set(${val.size})`]: [...val.values()].map(v => stringifySymbol(v))
    } : isSymbol(val) ? stringifySymbol(val) : isObject(val) && !isArray(val) && !isPlainObject(val) ? String(val) : val;
    
    const stringifySymbol = (v, i = "") => {
        var _a;
        return isSymbol(v) ? `Symbol(${(_a = v.description) != null ? _a : i})` : v;
    };

    /**
     * @vue/reactivity v3.4.38 - Reactivity system
     * 
     * Note: The original file contains the complete Vue.js reactivity system,
     * runtime-core, and runtime-dom implementations. This deobfuscated version
     * shows the structure and main utility functions. The complete implementation
     * would include:
     * 
     * - EffectScope class for managing reactive effects
     * - ReactiveEffect class for tracking dependencies
     * - Proxy handlers for reactive objects
     * - Ref implementation
     * - Computed properties
     * - Watch/watchEffect functions
     * - Component lifecycle hooks
     * - Virtual DOM implementation
     * - Template compilation utilities
     * - And much more...
     */

    // Export the Vue module
    return {
        // Shared utilities
        makeMap,
        EMPTY_OBJ,
        EMPTY_ARR,
        NOOP,
        extend,
        remove,
        hasOwn,
        isArray,
        isMap,
        isSet,
        isFunction,
        isString,
        isSymbol,
        isObject,
        isPromise,
        toTypeString,
        toRawType,
        isPlainObject,
        isIntegerKey,
        camelize,
        hyphenate,
        capitalize,
        toHandlerKey,
        hasChanged,
        invokeArrayFns,
        normalizeStyle,
        normalizeClass,
        toDisplayString,
        // ... and many more Vue.js APIs
    };
});
